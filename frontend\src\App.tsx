import React, { useState } from 'react';
import ApiConfig from './ApiConfig';

function App() {
  const [apiUrl, setApiUrl] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [activeSection, setActiveSection] = useState('api-config');

  const handleConfigChange = (url: string, key: string) => {
    setApiUrl(url);
    setApiKey(key);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* 侧边栏 */}
      <div className="w-64 bg-gray-800 text-white">
        <div className="p-4 border-b border-gray-700">
          <h1 className="text-xl font-bold">Acunetix Scanner</h1>
        </div>
        <nav className="p-2">
          <button
            onClick={() => setActiveSection('api-config')}
            className={`w-full text-left px-4 py-3 rounded-md mb-1 ${
              activeSection === 'api-config' 
                ? 'bg-gray-700 text-white' 
                : 'text-gray-300 hover:bg-gray-700'
            }`}
          >
            API Configuration
          </button>
          {apiUrl && apiKey && (
            <>
              <button
                onClick={() => setActiveSection('targets')}
                className={`w-full text-left px-4 py-3 rounded-md mb-1 ${
                  activeSection === 'targets' 
                    ? 'bg-gray-700 text-white' 
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                Scan Targets
              </button>
              <button
                onClick={() => setActiveSection('scans')}
                className={`w-full text-left px-4 py-3 rounded-md mb-1 ${
                  activeSection === 'scans' 
                    ? 'bg-gray-700 text-white' 
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                Scans
              </button>
              <button
                onClick={() => setActiveSection('reports')}
                className={`w-full text-left px-4 py-3 rounded-md mb-1 ${
                  activeSection === 'reports' 
                    ? 'bg-gray-700 text-white' 
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                Reports
              </button>
            </>
          )}
        </nav>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {activeSection === 'api-config' && (
            <ApiConfig onConfigChange={handleConfigChange} />
          )}
          
          {activeSection === 'targets' && apiUrl && apiKey && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">Scan Targets</h2>
                <p className="text-gray-600 mb-4">
                  Manage your scan targets. API URL: {apiUrl}
                </p>
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                  <p className="text-blue-700">
                    <strong>API Key:</strong> {apiKey ? '••••••••••••••••' : 'Not set'}
                  </p>
                </div>
                <p className="text-gray-500">
                  Implementation of scan targets functionality will be added here.
                </p>
              </div>
            </div>
          )}
          
          {activeSection === 'scans' && apiUrl && apiKey && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">Scans</h2>
                <p className="text-gray-600 mb-4">
                  Manage your scans. API URL: {apiUrl}
                </p>
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                  <p className="text-blue-700">
                    <strong>API Key:</strong> {apiKey ? '••••••••••••••••' : 'Not set'}
                  </p>
                </div>
                <p className="text-gray-500">
                  Implementation of scans functionality will be added here.
                </p>
              </div>
            </div>
          )}
          
          {activeSection === 'reports' && apiUrl && apiKey && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4 text-gray-800">Reports</h2>
                <p className="text-gray-600 mb-4">
                  Manage your reports. API URL: {apiUrl}
                </p>
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                  <p className="text-blue-700">
                    <strong>API Key:</strong> {apiKey ? '••••••••••••••••' : 'Not set'}
                  </p>
                </div>
                <p className="text-gray-500">
                  Implementation of reports functionality will be added here.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;