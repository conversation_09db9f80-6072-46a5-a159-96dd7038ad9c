import React, { useState, useEffect } from 'react';

interface ApiConfigProps {
  onConfigChange: (apiUrl: string, apiKey: string) => void;
}

const ApiConfig: React.FC<ApiConfigProps> = ({ onConfigChange }) => {
  const [apiUrl, setApiUrl] = useState('');
  const [apiKey, setApiKey] = useState('');

  useEffect(() => {
    const savedApiUrl = localStorage.getItem('acunetix_api_url') || '';
    const savedApiKey = localStorage.getItem('acunetix_api_key') || '';
    
    setApiUrl(savedApiUrl);
    setApiKey(savedApiKey);
    
    if (savedApiUrl && savedApiKey) {
      onConfigChange(savedApiUrl, savedApiKey);
    }
  }, [onConfigChange]);

  const handleSave = () => {
    localStorage.setItem('acunetix_api_url', apiUrl);
    localStorage.setItem('acunetix_api_key', apiKey);
    onConfigChange(apiUrl, apiKey);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">API Configuration</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API Base URL
          </label>
          <input
            type="text"
            value={apiUrl}
            onChange={(e) => setApiUrl(e.target.value)}
            placeholder="https://your-acunetix-instance.com/api/v1"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="mt-1 text-sm text-gray-500">
            Enter the base URL of your Acunetix installation (e.g., https://localhost:3443/api/v1)
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API Key
          </label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="Your API key"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="mt-1 text-sm text-gray-500">
            Enter your Acunetix API key (found in your Acunetix account settings)
          </p>
        </div>
        
        <div className="pt-4">
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Save Configuration
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApiConfig;