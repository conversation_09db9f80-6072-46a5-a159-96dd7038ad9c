{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.8.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^2.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^4.6.4", "vite": "^3.0.7"}}